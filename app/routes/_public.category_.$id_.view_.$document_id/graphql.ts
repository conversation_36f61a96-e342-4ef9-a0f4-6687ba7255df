import { graphql } from '~/gql'

export const GET_DOCUMENT_BY_ID = graphql(`
  query GetDocumentById($id: ID!) {
    getDocumentById(id: $id) {
    id
    title
    body
    tags
    added_date
    category {
      id
      name
    }
    extra_record {
      __typename
      ... on InneihRecord {
        ...InneihRecordFragment
      }
      ... on BaptismaRecord {
        ...BaptismaRecordFragment
      }
    }
    files {
      id
      path
      file_type
    }

    }
  }
`)
