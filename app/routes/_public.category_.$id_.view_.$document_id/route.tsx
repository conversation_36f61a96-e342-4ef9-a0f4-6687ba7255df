import { useParams } from 'react-router'
import { Bread<PERSON>rumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Card, CardContent } from '~/components/ui/card'
import BaptismaDocument from './baptisma-document'
import InneihDocument from './inneih-document'
import OthersDocument from './others-document'
import useGetDocumentById from './use-get-document-by-id'

export default function CategoryDocumentById() {
  const { document_id } = useParams()
  const { data, isLoading, isError } = useGetDocumentById(document_id!)

  const record = data?.getDocumentById?.extra_record
  const files = data?.getDocumentById?.files || []

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div>Error loading document</div>
  }

  return (
    <>
      <Card className="my-4 bg-white">
        <CardContent>
          <Breadcrumb className="pt-4 pb-4">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                /
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                <BreadcrumbLink href={`/category/${data?.getDocumentById?.category?.id}`}>
                  {data?.getDocumentById?.category?.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                /
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                {data?.getDocumentById?.title}
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </CardContent>
      </Card>
      {record && record.__typename === 'BaptismaRecord' && (
        <BaptismaDocument record={record} files={files} />
      )}
      {record && record.__typename === 'InneihRecord' && (
        <InneihDocument record={record} files={files} />
      )}
      {!record && (
        <OthersDocument document={data?.getDocumentById} files={files} />
      )}
    </>
  )
}
