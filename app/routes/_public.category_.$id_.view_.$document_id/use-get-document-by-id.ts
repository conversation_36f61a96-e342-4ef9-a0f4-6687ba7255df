import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENT_BY_ID } from './graphql'

export default function useGetDocumentById(id: string) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-document-by-id', id],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENT_BY_ID,
        variables: {
          id,
        },
      })
    },
  })

  return { data, isLoading, isError }
}
