import { useMutation, useQueryClient } from "@tanstack/react-query"
import { graphqlClient } from "~/lib/graphql-client"
import { DELETE_DOCUMENT } from "./graphql"
import { toast } from "sonner"
import { parseGraphqlError } from "~/lib/parse-graphql-error"

export default function useDeleteDocument() {
  const queryClient = useQueryClient()

  const deleteDocument = useMutation({
    mutationFn: async (document_id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_DOCUMENT,
        variables: {
          document_id,
        },
      })
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['get-baptisma'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-inneih'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-others'],
      })
      toast.success("Document deleted")
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteDocument }
}