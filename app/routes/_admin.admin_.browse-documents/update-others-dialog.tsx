import type { UpdateRecordType } from './schema'
import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import useUpdateDocuments from './use-update-documents'

type DocumentFromQuery = NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  _document: DocumentFromQuery
}

export default function UpdateOthersDialog({ isOpen, toggle, _document }: Props) {
  const { updateOthersRecord } = useUpdateDocuments()

  const form = useAppForm({
    defaultValues: {
      document: {
        id: _document.id,
        title: _document.title || '',
        body: _document.body || '',
        tags: _document.tags || '',
        is_classified: _document.is_classified || false,
        category_id: _document.category_id || '',
        added_date: _document.added_date ? format(new Date(_document.added_date), 'yyyy-MM-dd') : '',
        files: undefined as File[] | undefined,
      },
    } as UpdateRecordType,
    onSubmit: async ({ value}) => {
      await updateOthersRecord.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent className="w-full min-w-256">
        <DialogHeader>
          <DialogTitle>
            Update Document
          </DialogTitle>
          <DialogDescription>
            Enter update details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.title"
                children={field => <field.InputField label="Title" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.tags"
                children={field => <field.PillInput label="Tags" />}
              />
            </div>
          </div>
          <div className="w-full">
            <form.AppField
              name="document.body"
              children={field => <field.InputRichText label="Body" />}
            />
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.Field
                name="document.files"
                children={field => (
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Upload file</div>
                    <Input
                      type="file"
                      multiple={true}
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          field.handleChange(Array.from(e.target.files))
                        }
                        else {
                          field.handleChange([])
                        }
                      }}
                      className="bg-white"
                    />

                  </Label>
                )}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.added_date"
                children={field => <field.InputField type="date" label="Added date" />}
              />
            </div>
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.category_id"
                children={field => <field.InputField label="Category ID" />}
              />
            </div>
          </div>
          <div>
            <form.AppField
              name="document.is_classified"
              children={field => <field.CheckboxField label="Classified" />}
            />
          </div>
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}