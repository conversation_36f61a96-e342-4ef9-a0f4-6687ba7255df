import type { GetDocumentsQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import PagePagination from '~/components/common/page-pagination'
import UpdateIcon from '~/components/icons/update-icon'
import DeleteIcon from '~/components/icons/delete-icon'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import useGetDocuments from './use-get-documents'
import useDeleteDocument from './use-delete-document'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import UpdateOthersDialog from './update-others-dialog'

export default function OthersFilters() {
  const { getOthers, handlePage, page, searchOthers } = useGetDocuments()
  const [selectedDocument, setSelectedDocument] = useState<NonNullable<GetDocumentsQuery['getDocuments']['data']>[0]>()
  const [selectedId, setSelectedId] = useState('')
  const { deleteDocument } = useDeleteDocument()

  const { isOpen, toggle } = useBoolean()
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteDocument.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  const form = useAppForm({
    defaultValues: {
      title: '',
      body: '',
      tag: '',
      added_date: '',
    },
    onSubmit: async ({ value }) => {
      searchOthers(value)
    },
  })

  const data = getOthers.data?.getDocuments?.data || []
  const lastPage = getOthers.data?.getDocuments?.paginator_info?.last_page ?? 1

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="title"
              children={field => <field.InputField label="Title" />}
            />
            <form.AppField
              name="body"
              children={field => <field.InputField label="Body" />}
            />
            <form.AppField
              name="tag"
              children={field => <field.PillInput label="Tags" />}
            />
            <form.AppField
              name="added_date"
              children={field => <field.InputField label="Added date" type="date" />}
            />

            <div className="col-span-1">
              <Button type="submit" isLoading={getOthers.isLoading}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <div className="flex grow rounded-md bg-muted p-4">
          <Table className="w-full min-w-[1000px] table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Title</TableHead>
                <TableHead className="w-64">Body</TableHead>
                <TableHead className="w-48">Tags</TableHead>
                <TableHead className="w-24">Attached files</TableHead>
                <TableHead className="w-32">Added on</TableHead>
                <TableHead className="w-32">Classified</TableHead>
                <TableHead className="w-32 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.map(item => (
                item && (
                  <TableRow key={item.id}>
                    <TableCell>
                      {item.title || '-'}
                    </TableCell>
                    <TableCell>
                      {item.body
                        ? (
                            <div
                              dangerouslySetInnerHTML={{ __html: item.body }}
                              className="whitespace-pre-line"
                            />
                          )
                        : '-'}
                    </TableCell>
                    <TableCell>{item.tags || '-'}</TableCell>
                    <TableCell>{item.files?.length || '0'}</TableCell>
                    <TableCell>{item.added_date ? format(new Date(item.added_date), 'yyyy-MM-dd') : '-' }</TableCell>
                    <TableCell>{item.is_classified ? 'Yes' : 'No'}</TableCell>
                    <TableCell>
                      <div className="flex justify-end gap-x-2">
                        <AppTooltip message="Update">
                          <Button
                            onClick={() => {
                              setSelectedDocument(item)
                              toggle(true)
                            }}
                            size="icon"
                            variant="success"
                          >
                            <UpdateIcon />
                          </Button>
                        </AppTooltip>
                        <AppTooltip message="Delete document">
                          <Button
                            onClick={() => {
                              if (item.id) {
                                setSelectedId(item.id)
                                toggleDelete(true)
                              }
                            }}
                            size="icon"
                            variant="destructive"
                          >
                            <DeleteIcon />
                          </Button>
                        </AppTooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                )))}
            </TableBody>
          </Table>

        </div>
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
      {selectedDocument && (
        <UpdateOthersDialog
          isOpen={isOpen}
          toggle={(open) => {
            toggle(open)
            setSelectedDocument(undefined)
          }}
          _document={selectedDocument}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteDocument.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
    </>
  )
}
