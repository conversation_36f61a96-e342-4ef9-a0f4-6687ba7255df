import type { GetDocumentsByCategoryIdQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useNavigate } from 'react-router'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'

interface Props {
  documents: GetDocumentsByCategoryIdQuery['getDocumentsByCategoryId']['data']
}

export default function OthersTable({ documents }: Props) {
  const navigate = useNavigate()

  return (
    <div className="my-4 flex grow rounded-md bg-white p-4">
      <Table className="w-full min-w-[1000px] table-fixed">
        <TableHeader>
          <TableRow>
            <TableHead className="w-2/3">
              Document Name
            </TableHead>
            <TableHead>
              Date
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents && documents.length > 0
            ? documents.map(document => (
                <TableRow
                  onClick={() => {
                    navigate(`/category/${document.category?.id}/view/${document.id}`)
                  }}
                  key={document.id}
                >
                  <TableCell>
                    {document.title}
                  </TableCell>
                  <TableCell>
                    {document.added_date ? format(new Date(document.added_date), 'dd-MMM-yyyy') : '-'}
                  </TableCell>
                </TableRow>
              ))
            : (
                <TableRow>
                  <TableCell
                    colSpan={2}
                    className="text-center text-muted-foreground"
                  >
                    No records found
                  </TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>

    </div>
  )
}
