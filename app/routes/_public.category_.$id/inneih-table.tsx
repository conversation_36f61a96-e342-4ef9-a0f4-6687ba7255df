import type { FragmentType } from '~/gql'
import type { GetDocumentsByCategoryIdQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { useNavigate } from 'react-router'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useFragment } from '~/gql'
import { INNEIH_RECORD_FRAGMENT } from './graphql'

type Document = NonNullable<GetDocumentsByCategoryIdQuery['getDocumentsByCategoryId']['data']>[number]

interface Props {
  documents: Document[]
}

function InneihRecordRow({
  documentId,
  inneihRecord,
}: {
  documentId: string
  inneihRecord: FragmentType<typeof INNEIH_RECORD_FRAGMENT>
}) {
  const record = useFragment(INNEIH_RECORD_FRAGMENT, inneihRecord)
  const navigate = useNavigate()

  return (
    <TableRow
      key={documentId}
      onClick={() => {
        navigate(`/category/5/view/${documentId}`)
      }}
    >
      <TableCell>{record.inneih_registration_no || '-'}</TableCell>
      <TableCell>
        {record.mipa_hming}
        ,
        {' '}
        {record.hmeichhe_hming}
      </TableCell>
      <TableCell>{record.mipa_pa_hming}</TableCell>
      <TableCell>{record.hmeichhe_pa_hming}</TableCell>
      <TableCell>{record.inneihtirtu}</TableCell>
      <TableCell>{record.hmun}</TableCell>
      <TableCell>{record.inneih_ni ? format(new Date(record.inneih_ni), 'dd-MMM-yyyy') : '-'}</TableCell>
    </TableRow>
  )
}

export default function InneihTable({ documents }: Props) {
  const inneihDocuments = documents.filter((doc): doc is Document & {
    extra_record: NonNullable<Document['extra_record']> & { __typename: 'InneihRecord' }
  } =>
    doc.extra_record != null
    && doc.extra_record.__typename === 'InneihRecord',
  )

  return (
    <div className="my-4 flex grow rounded-md bg-white p-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-32">Reg no</TableHead>
            <TableHead className="w-32">Inneihte Nupa Hming</TableHead>
            <TableHead className="w-32">Mipa Pa Hming</TableHead>
            <TableHead className="w-32">Hmeichhe Pa Hming</TableHead>
            <TableHead className="w-32">Inneihtirtu Hming</TableHead>
            <TableHead className="w-32">Veng Hming</TableHead>
            <TableHead className="w-32">Inneih Ni</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {inneihDocuments.length > 0
            ? (
                inneihDocuments.map(document => (
                  <InneihRecordRow
                    key={document.id}
                    documentId={document.id}
                    inneihRecord={document.extra_record}
                  />
                ))
              )
            : (
                <TableRow>
                  <TableCell className="text-center text-muted-foreground" colSpan={7}>No records found</TableCell>
                </TableRow>
              )}
        </TableBody>
      </Table>
    </div>
  )
}
