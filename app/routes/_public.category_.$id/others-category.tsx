import type { OtherRecordInput } from '~/gql/graphql'
import { SearchIcon, X } from 'lucide-react'
import { useState } from 'react'
import { Accordion, AccordionContent, AccordionItem } from '~/components/ui/accordion'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'

interface Props {
  searchOthers: (data: OtherRecordInput | null) => void
  isLoading: boolean
  category: string | undefined
}
export default function OthersCategory({ searchOthers, isLoading, category }: Props) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  const form = useAppForm({
    defaultValues: {
      title: '',
      body: '',
      tag: '',
      added_date: '',
    },
    onSubmit: async ({ value }) => {
      // return null if all fields are empty
      const isEmpty = Object.values(value).every(val => !val || val === '')
      searchOthers(isEmpty ? null : value)
    },
  })

  return (
    <>
      <Card className="my-4 bg-white">
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <Breadcrumb className="pt-4 pb-4">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator>
                    /
                  </BreadcrumbSeparator>
                  <BreadcrumbItem>
                    {category}
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
            {isSearchOpen
              ? (
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => setIsSearchOpen(!isSearchOpen)}
                  >
                    <X className="text-xl" />
                  </Button>
                )
              : (
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => setIsSearchOpen(!isSearchOpen)}
                  >
                    <SearchIcon className="text-xl" />
                  </Button>
                )}
          </div>
          <Accordion
            type="single"
            value={isSearchOpen ? 'search-form' : ''}
            className="border-none"
          >
            <AccordionItem value="search-form" className="border-none">
              <AccordionContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4 pt-4"
          >
            <form.AppField
              name="title"
              children={field => <field.InputField label="Title" />}
            />
            <form.AppField
              name="body"
              children={field => <field.InputField label="Body" />}
            />
            <form.AppField
              name="tag"
              children={field => <field.PillInput label="Tags" />}
            />
            <form.AppField
              name="added_date"
              children={field => <field.InputField label="Added date" type="date" />}
            />

            <div className="col-span-1">
              <Button type="submit" isLoading={isLoading}>
                Search
              </Button>
            </div>
          </form>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </>
  )
}
