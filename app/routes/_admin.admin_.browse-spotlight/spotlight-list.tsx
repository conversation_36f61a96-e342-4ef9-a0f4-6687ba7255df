import type { GetSpotlightsQuery } from '~/gql/graphql'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import CommonError from '~/components/common/common-error'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import { CardLoader } from '~/components/common/loaders'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import useBoolean from '~/hooks/use-boolean'
import useGetSpotlights from '~/hooks/use-get-spotlights'
import { baseUrl } from '~/lib/base-url'
import UpdateSpotlightDialog from './update-spotlight-dialog'
import useDeleteSpotlight from './use-delete-spotlight'

export default function SpotlightList() {
  const { data, isLoading, handlePage, isError, page, lastPage } = useGetSpotlights({ first: 8 })
  const { isOpen: openDelete, toggle: toggleDelete } = useBoolean()
  const { isOpen: openUpdate, toggle: toggleUpdate } = useBoolean()
  const [selectedId, setSelectedId] = useState('')
  const [selectedSpotlight, setSelectedSpotlight] = useState<GetSpotlightsQuery['getSpotlights']['data'][number]>()
  const { deleteSpotlight } = useDeleteSpotlight()

  const handleDelete = () => {
    if (!selectedId) {
      return
    }
    deleteSpotlight.mutate(selectedId, {
      onSuccess: () => {
        setSelectedId('')
      },
    })
  }

  if (isLoading) {
    return (
      <div className="grid grow grid-cols-4 gap-4">
        <CardLoader className="col-span-1 size-full" length={8} />
      </div>
    )
  }

  if (isError) {
    return (
      <CommonError />
    )
  }

  return (
    <>
      <div className="grid grid-cols-4 gap-4">
        {data?.getSpotlights?.data?.map((spotlight) => {
          return (
            <div
              key={spotlight.id}
              className="col-span-1 flex flex-col rounded-md bg-gray-100 p-4"
            >
              <div className="flex flex-col">
                <div>
                  <img
                    className={`
                      aspect-video h-full w-full rounded-md object-cover
                    `}
                    src={`${baseUrl}/image/${spotlight.file_path}`}
                  />

                </div>
                <div className="mt-4 text-xl font-bold">{spotlight.title}</div>
                <div>{spotlight.body}</div>
              </div>

              <div className="mt-4 flex justify-between">
                <AppTooltip message="Delete">
                  <Button
                    onClick={() => {
                      setSelectedId(spotlight.id)
                      toggleDelete(true)
                    }}
                    variant="destructive"
                    size="icon"
                  >
                    <DeleteIcon />
                  </Button>
                </AppTooltip>
                <AppTooltip message="Update">
                  <Button
                    onClick={() => {
                      setSelectedSpotlight(spotlight)
                      toggleUpdate(true)
                    }}
                    variant="success"
                    size="icon"
                  >
                    <UpdateIcon />
                  </Button>
                </AppTooltip>
              </div>
            </div>
          )
        })}
      </div>
      {selectedSpotlight && (
        <UpdateSpotlightDialog
          isOpen={openUpdate}
          toggle={toggleUpdate}
          spotlight={selectedSpotlight}
        />
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteSpotlight.isPending}
          handleConfirm={handleDelete}
          open={openDelete}
          handleOpenChange={toggleDelete}
        />
      )}
      {lastPage > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePage}
          lastPage={lastPage}
        />
      )}
    </>
  )
}
